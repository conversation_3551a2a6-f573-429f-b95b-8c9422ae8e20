import 'dart:async';

import 'package:flutter/material.dart';
import 'package:fusion/utils/time_formatter.dart';
import 'package:get/get.dart';

typedef CountdownBuilder = Widget Function(
    BuildContext context, int remaining, String formatted);

class Countdown extends StatefulWidget {
  const Countdown({
    super.key,
    required this.builder,
    required this.times,
    this.formatter = autoFitFormatter,
    this.onExpired,
  });

  /// 计时秒数
  final int times;

  /// 格式化器
  final String Function(int num) formatter;

  /// 构建器
  final CountdownBuilder builder;

  /// 过期回调
  final VoidCallback? onExpired;

  @override
  State<Countdown> createState() => _CountdownState();
}

class _CountdownState extends State<Countdown>
    with AutomaticKeepAliveClientMixin {
  /// 剩余时间秒数
  final remaining = 0.obs;

  /// 倒计时开始的时间。使用现在的时间 - 开始时间 = 过去的时间
  var _startTime = DateTime.now();

  Timer? _timer;

  _expired() {
    _timer?.cancel();
    _timer = null;
    widget.onExpired?.call();
  }

  _updateRemaining() {
    final pass = (DateTime.now().millisecondsSinceEpoch -
            _startTime.millisecondsSinceEpoch) ~/
        1000;
    remaining.value = widget.times - pass;
  }

  _tick(Timer timer) {
    if (remaining.value <= 0) {
      _expired();
      return;
    }
    _updateRemaining();
  }

  _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), _tick);
  }

  @override
  void initState() {
    super.initState();
    _updateRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (widget.times <= 0) {
      return widget.builder(context, 0, widget.formatter(0));
    }
    return Obx(
      () => widget.builder(
        context,
        remaining.value,
        widget.formatter(remaining.value),
      ),
    );
  }

  @override
  void didUpdateWidget(covariant Countdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.times != widget.times) {
      _startTime = DateTime.now();
    }
  }

  @override
  bool get wantKeepAlive => true;
}
