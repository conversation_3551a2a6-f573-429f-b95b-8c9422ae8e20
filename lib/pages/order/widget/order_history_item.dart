import 'package:flutter/material.dart';
import 'package:fusion/extension/double.dart';
import 'package:fusion/gen/app.gaps.dart';
import 'package:fusion/gen/colors.gen.dart';
import 'package:fusion/pages/order/widget/wear_sticker_row.dart';
import 'package:fusion/router/app_router.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/theme/filled_button.dart';
import 'package:fusion/widgets/accessory_cover.dart';
import 'package:fusion/widgets/count_down.dart';
import 'package:fusion/widgets/extension_on_widget_iterable.dart';
import 'package:get/get.dart';

import 'order_item_data.dart';

typedef OrderHistoryItemAction<T> = void Function(T data, OrderAction action);

/// 动作按钮样式
extension E on OrderAction {
  ButtonStyle buttonStyle() {
    switch (this) {
      case OrderAction.pay:
      case OrderAction.createTradeOffer:
      case OrderAction.acceptTradeOffer:
      case OrderAction.delivery:
      case OrderAction.pauseWant:
      case OrderAction.resumeWant:
      case OrderAction.changeSellingPrice:
        return filledButtonStyle.copyWith(
          minimumSize: WidgetStateProperty.all(const Size(64, 32)),
        );
      case OrderAction.cancelWant:
        return filledButtonStyleLightBlue.copyWith(
          minimumSize: WidgetStateProperty.all(const Size(64, 32)),
        );
      case OrderAction.tradeProtection:
        return filledButtonStyleDisabled.copyWith(
          minimumSize: WidgetStateProperty.all(const Size(64, 32)),
        );
    }
  }
}

class OrderHistoryItem extends StatelessWidget {
  final OrderItemData data;
  final OrderHistoryItemAction<OrderItemData>? onAction;
  final VoidCallback? onExpired;

  /// 插入图片后面的控件
  final List<Widget>? afterImageLines;

  /// 插入底部的控件。会在Column的children中
  final List<Widget>? trailing;
  const OrderHistoryItem({
    super.key,
    required this.data,
    this.onAction,
    this.trailing,
    this.onExpired,
    this.afterImageLines,
  });

  Iterable<Widget> _buildActions() {
    if (data.actions == null) {
      return [];
    }
    return data.actions!
        .map(
          (e) => FilledButton(
            style: e.buttonStyle(),
            onPressed: () {
              onAction?.call(data, e);
            },
            child: Text(e.actionText),
          ),
        )
        .spaceBetween(10);
  }

  @override
  Widget build(BuildContext context) {
    const topHighlightStyle = TextStyle(
      color: AppColors.orange,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (data.displayTopBar) ...[
            Row(
              children: [
                // 顶部时间
                if (data.topDateTime != null)
                  Text(
                    data.topDateTime!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.gray888,
                    ),
                  ),
                const Spacer(),
                // 状态
                Text(
                  data.stateMessage.toString(),
                  style: topHighlightStyle.copyWith(
                    color: data.stateMessageColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                // 倒计时
                if (!data.useInlineCountdown &&
                    data.state.enableCountdown &&
                    data.orderStatusCountdownEffective) ...[
                  Gaps.h2,
                  _buildCountdown(),
                ],
                if (data.state == OrderState.tradeProtection) ...[
                  IconButton(
                    padding: const EdgeInsets.all(6),
                    onPressed: () {
                      AppRouter.to.push(
                        MessageDialogRoute(
                          message: data.buyerUid == UserStore.to.uId
                              ? "交易报价已完成，您已收到饰品，该订单正在保护期中。保护期间请勿在Steam撤销交易，如果违规撤销，对应的订单将不会结束或终止，所支付的订单金额将继续结算给卖家，不会退还给您。"
                              : "该订单正在保护期中，保护期结束后才能完成交易收到出售金额。保护期间请勿在Steam进行撤销，如果违规撤销，平台会永久封禁当前账号以及同实名的其他账号，同时注册手机号、绑定的Steam账号也会被永久封禁",
                          okButtonTitle: "我知道了",
                          onlyShowOkButton: true,
                        ),
                      );
                    },
                    icon: const Icon(
                      Icons.info_outline_rounded,
                      color: AppColors.orange,
                      size: 16,
                    ),
                  )
                ],
                if (data.state == OrderState.tradeOfferPending) ...[
                  IconButton(
                    padding: const EdgeInsets.all(6),
                    onPressed: () {
                      AppRouter.to.push(
                        MessageDialogRoute(
                          message:
                              "暂挂订单一般48小时会自动完成， 建议双方等待这个暂挂期结束，不要主动取消这个交易，否则会触发Steam的7天交易冷却安全限制！",
                          okButtonTitle: "我知道了",
                          onlyShowOkButton: true,
                        ),
                      );
                    },
                    icon: const Icon(
                      Icons.info_outline_rounded,
                      color: AppColors.orange,
                      size: 16,
                    ),
                  )
                ]
              ],
            ),
            Gaps.v16,
          ],
          SizedBox(
            height: 66,
            child: Row(
              children: [
                // 封面图
                AccessoryCover(
                  url: data.coverUrls.first,
                  tags: data.coverTags,
                ),
                Gaps.h8,
                // 名称+价格
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 名称
                            Expanded(
                              child: Text(
                                data.name,
                                softWrap: true,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                            // 和名称一行的动作按钮order_history_item
                            if (data.useInlineActions) ...[
                              Gaps.h10,
                              ..._buildActions(),
                            ],
                            if (data.useInlineStateMessage) ...[
                              Gaps.h10,
                              ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxWidth: context.width * 0.16,
                                ),
                                child: Text(
                                  data.stateMessage.toString(),
                                  style: topHighlightStyle.copyWith(
                                    color: data.stateMessageColor,
                                  ),
                                ),
                              ),
                            ]
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          // 价格
                          Text(
                            "¥${data.price.removeTrailingZero()}",
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.redEC292D,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          // 价格说明，使用括号包含的
                          if (data.priceTip != null)
                            Text(
                              "（${data.priceTip}）",
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.gray,
                              ),
                            ),
                          // 划线价格
                          if (data.expiredPrice != null)
                            Text(
                              "¥${data.expiredPrice}",
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.black,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          const Spacer(),
                          // 求购进度
                          if (data.type == OrderType.want)
                            Text(
                              "${data.finishedCount}/${data.requiredCount}",
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.gray,
                              ),
                            ),
                          // 倒计时
                          if (data.useInlineCountdown) _buildCountdown(),
                        ],
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          if (afterImageLines?.isNotEmpty == true) ...[
            Gaps.v16,
            ...?afterImageLines
          ],
          // 磨损 + 印花
          WearStickerRow(
            wear: data.wear,
            stickers: data.stickers,
            style: data.styleName ?? '',
          ),
          if (data.sellerMessage != null)
            // 卖家留言
            Text(
              "卖家留言：${data.sellerMessage}",
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.gray,
              ),
            ),
          // 支付方式
          if (data.displayPaymentMethod ||
              (data.enableActions &&
                  data.actions != null &&
                  !data.useInlineActions)) ...[
            Gaps.v16,
            Row(
              children: [
                if (data.displayPaymentMethod) ...[
                  data.paymentMethod.getWidget(),
                  Gaps.h8,
                  Text(
                    data.paymentMethodName!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.gray,
                    ),
                  ),
                ],
                const Spacer(),
                ..._buildActions(),
              ],
            ),
          ],
          ...?trailing,
        ],
      ),
    );
  }

  Widget _buildCountdown() {
    return Countdown(
      key: ValueKey("${data.id}-${data.orderStatusCountdown}"),
      builder: (c, r, f) {
        return Text(
          r > 0 ? f : "",
          style: TextStyle(
            color: data.stateMessageColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
      },
      times: data.orderStatusCountdown,
      onExpired: onExpired,
    );
  }
}
